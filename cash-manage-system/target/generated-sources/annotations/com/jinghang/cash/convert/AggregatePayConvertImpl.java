package com.jinghang.cash.convert;

import com.jinghang.cash.modules.manage.vo.req.AggregatePaymentConfigRequest;
import com.jinghang.cash.modules.manage.vo.rsp.AggregatePaymentConfigResp;
import com.jinghang.cash.pojo.AggregatePaymentConfig;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-26T16:47:50+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
public class AggregatePayConvertImpl implements AggregatePayConvert {

    @Override
    public AggregatePaymentConfigResp toAggregatePaymentConfigResp(AggregatePaymentConfig paymentConfig) {
        if ( paymentConfig == null ) {
            return null;
        }

        AggregatePaymentConfigResp aggregatePaymentConfigResp = new AggregatePaymentConfigResp();

        aggregatePaymentConfigResp.setId( paymentConfig.getId() );
        aggregatePaymentConfigResp.setBusinessSubject( paymentConfig.getBusinessSubject() );
        aggregatePaymentConfigResp.setCollectSubject( paymentConfig.getCollectSubject() );
        aggregatePaymentConfigResp.setAliAccount( paymentConfig.getAliAccount() );
        aggregatePaymentConfigResp.setWechatAccount( paymentConfig.getWechatAccount() );
        aggregatePaymentConfigResp.setTransferAccount( paymentConfig.getTransferAccount() );

        return aggregatePaymentConfigResp;
    }

    @Override
    public AggregatePaymentConfig toAggregatePaymentConfig(AggregatePaymentConfigRequest request) {
        if ( request == null ) {
            return null;
        }

        AggregatePaymentConfig aggregatePaymentConfig = new AggregatePaymentConfig();

        aggregatePaymentConfig.setId( request.getId() );
        aggregatePaymentConfig.setBusinessSubject( request.getBusinessSubject() );
        aggregatePaymentConfig.setCollectSubject( request.getCollectSubject() );
        aggregatePaymentConfig.setAliAccount( request.getAliAccount() );
        aggregatePaymentConfig.setWechatAccount( request.getWechatAccount() );
        aggregatePaymentConfig.setTransferAccount( request.getTransferAccount() );

        return aggregatePaymentConfig;
    }
}
