package com.jinghang.cash.convert;

import com.jinghang.cash.enums.BankChannel;
import com.jinghang.cash.enums.RepayPurpose;
import com.jinghang.cash.modules.manage.vo.req.ReduceApplyReq;
import com.jinghang.cash.modules.manage.vo.req.RepayApplyReq;
import com.jinghang.cash.modules.manage.vo.rsp.RepayPlanRsp;
import com.jinghang.cash.modules.manage.vo.rsp.TrialRes;
import com.jinghang.cash.pojo.RepayPlan;
import com.jinghang.ppd.api.dto.TrailResultDto;
import com.jinghang.ppd.api.dto.repay.ReduceApplyDto;
import com.jinghang.ppd.api.dto.repay.RepayApplyDto;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-26T17:31:26+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.12 (Oracle Corporation)"
)
public class AfterLoanConvertImpl implements AfterLoanConvert {

    @Override
    public RepayPlanRsp toRepayPlanRsp(RepayPlan repayPlan) {
        if ( repayPlan == null ) {
            return null;
        }

        RepayPlanRsp repayPlanRsp = new RepayPlanRsp();

        repayPlanRsp.setPeriod( repayPlan.getPeriod() );
        repayPlanRsp.setPlanRepayDate( repayPlan.getPlanRepayDate() );
        repayPlanRsp.setPrincipalAmt( repayPlan.getPrincipalAmt() );
        repayPlanRsp.setInterestAmt( repayPlan.getInterestAmt() );
        repayPlanRsp.setGuaranteeAmt( repayPlan.getGuaranteeAmt() );
        repayPlanRsp.setConsultFee( repayPlan.getConsultFee() );
        repayPlanRsp.setPenaltyAmt( repayPlan.getPenaltyAmt() );
        repayPlanRsp.setAmount( repayPlan.getAmount() );
        repayPlanRsp.setCustRepayState( repayPlan.getCustRepayState() );
        repayPlanRsp.setActRepayTime( repayPlan.getActRepayTime() );
        repayPlanRsp.setActPrincipalAmt( repayPlan.getActPrincipalAmt() );
        repayPlanRsp.setActInterestAmt( repayPlan.getActInterestAmt() );
        repayPlanRsp.setActGuaranteeAmt( repayPlan.getActGuaranteeAmt() );
        repayPlanRsp.setActPenaltyAmt( repayPlan.getActPenaltyAmt() );
        repayPlanRsp.setActBreachAmt( repayPlan.getActBreachAmt() );
        repayPlanRsp.setActConsultFee( repayPlan.getActConsultFee() );
        repayPlanRsp.setActAmount( repayPlan.getActAmount() );

        return repayPlanRsp;
    }

    @Override
    public TrialRes toTrialRes(TrailResultDto trailResultDto) {
        if ( trailResultDto == null ) {
            return null;
        }

        TrialRes trialRes = new TrialRes();

        trialRes.setPrincipal( trailResultDto.getPrincipal() );
        trialRes.setInterest( trailResultDto.getInterest() );
        trialRes.setPenalty( trailResultDto.getPenalty() );
        trialRes.setBreachFee( trailResultDto.getBreachFee() );
        trialRes.setGuaranteeFee( trailResultDto.getGuaranteeFee() );
        trialRes.setConsultFee( trailResultDto.getConsultFee() );
        trialRes.setAmount( trailResultDto.getAmount() );

        return trialRes;
    }

    @Override
    public ReduceApplyDto toReduceApplyDto(ReduceApplyReq reduceApplyReq) {
        if ( reduceApplyReq == null ) {
            return null;
        }

        ReduceApplyDto reduceApplyDto = new ReduceApplyDto();

        reduceApplyDto.setRepayPurpose( toRepayPurpose( reduceApplyReq.getRepayPurpose() ) );
        reduceApplyDto.setPeriod( reduceApplyReq.getPeriod() );
        reduceApplyDto.setOrderId( reduceApplyReq.getOrderId() );
        reduceApplyDto.setReduceAmount( reduceApplyReq.getReduceAmount() );
        reduceApplyDto.setRemark( reduceApplyReq.getRemark() );

        return reduceApplyDto;
    }

    @Override
    public RepayApplyDto toRepayApplyDto(RepayApplyReq req) {
        if ( req == null ) {
            return null;
        }

        RepayApplyDto repayApplyDto = new RepayApplyDto();

        repayApplyDto.setAmount( req.getActAmount() );
        repayApplyDto.setRepayPurpose( toRepayPurpose( req.getRepayPurpose() ) );
        repayApplyDto.setPeriod( req.getPeriod() );
        repayApplyDto.setOrderId( req.getOrderId() );

        return repayApplyDto;
    }

    @Override
    public com.jinghang.ppd.api.enums.RepayPurpose toRepayPurpose(RepayPurpose repayPurpose) {
        if ( repayPurpose == null ) {
            return null;
        }

        com.jinghang.ppd.api.enums.RepayPurpose repayPurpose1;

        switch ( repayPurpose ) {
            case CURRENT: repayPurpose1 = com.jinghang.ppd.api.enums.RepayPurpose.CURRENT;
            break;
            case CLEAR: repayPurpose1 = com.jinghang.ppd.api.enums.RepayPurpose.CLEAR;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + repayPurpose );
        }

        return repayPurpose1;
    }

    @Override
    public BankChannel toCore(BankChannel bankChannel) {
        if ( bankChannel == null ) {
            return null;
        }

        BankChannel bankChannel1;

        switch ( bankChannel ) {
            case TC_ZB: bankChannel1 = BankChannel.TC_ZB;
            break;
            case QJ_LSB: bankChannel1 = BankChannel.QJ_LSB;
            break;
            case RL_CYCFC: bankChannel1 = BankChannel.RL_CYCFC;
            break;
            case QJ_YL_F003: bankChannel1 = BankChannel.QJ_YL_F003;
            break;
            case ZY_EBANK: bankChannel1 = BankChannel.ZY_EBANK;
            break;
            case LH_RL_QH: bankChannel1 = BankChannel.LH_RL_QH;
            break;
            case QJ_HRB: bankChannel1 = BankChannel.QJ_HRB;
            break;
            case RL_QDB: bankChannel1 = BankChannel.RL_QDB;
            break;
            case ZXB: bankChannel1 = BankChannel.ZXB;
            break;
            case ZY_ZB_QH: bankChannel1 = BankChannel.ZY_ZB_QH;
            break;
            case QJ_LZB: bankChannel1 = BankChannel.QJ_LZB;
            break;
            case GMXT_TZ30_2_1: bankChannel1 = BankChannel.GMXT_TZ30_2_1;
            break;
            case CYBK: bankChannel1 = BankChannel.CYBK;
            break;
            case JMX_ZX: bankChannel1 = BankChannel.JMX_ZX;
            break;
            case ZZ_LN: bankChannel1 = BankChannel.ZZ_LN;
            break;
            case HAIER_QH: bankChannel1 = BankChannel.HAIER_QH;
            break;
            case QJ_LHB: bankChannel1 = BankChannel.QJ_LHB;
            break;
            case QJ_LH_ZS: bankChannel1 = BankChannel.QJ_LH_ZS;
            break;
            case CYBK_FL: bankChannel1 = BankChannel.CYBK_FL;
            break;
            case ZZR_ZB: bankChannel1 = BankChannel.ZZR_ZB;
            break;
            case ZXB_NUL: bankChannel1 = BankChannel.ZXB_NUL;
            break;
            case RL_CYCFC_FL: bankChannel1 = BankChannel.RL_CYCFC_FL;
            break;
            case TL_SMB: bankChannel1 = BankChannel.TL_SMB;
            break;
            case FBANK_LYX: bankChannel1 = BankChannel.FBANK_LYX;
            break;
            case ZXB_ZGC: bankChannel1 = BankChannel.ZXB_ZGC;
            break;
            case ZKJ_LN: bankChannel1 = BankChannel.ZKJ_LN;
            break;
            case HQB_CYB: bankChannel1 = BankChannel.HQB_CYB;
            break;
            case HAIER_LH_QH: bankChannel1 = BankChannel.HAIER_LH_QH;
            break;
            default: throw new IllegalArgumentException( "Unexpected enum constant: " + bankChannel );
        }

        return bankChannel1;
    }
}
