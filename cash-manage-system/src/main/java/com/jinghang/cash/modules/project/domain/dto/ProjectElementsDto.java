package com.jinghang.cash.modules.project.domain.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.api.enums.ProjectDurationType;
import com.jinghang.cash.api.enums.RepaymentType;
import com.jinghang.cash.enums.AbleStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 项目要素实体
 *
 * @Author: Lior
 * @CreateTime: 2025/8/20 13:39
 */
@Getter
@Setter
public class ProjectElementsDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 关联的项目唯一编码
     */
    private String projectCode;

    /**
     * 可提现范围(元) (格式 如 1000-50000)
     */
    private String drawableAmountRange;

    /**
     * 单笔提现步长(元)
     */
    private String drawableAmountStep;

    /**
     * 授信黑暗期 (格式 HH:mm-HH:mm)
     */
    private String creditDarkHours;

    /**
     * 用信黑暗期 (格式 HH:mm-HH:mm)
     */
    private String loanDarkHours;

    /**
     * 还款黑暗期 (格式 HH:mm-HH:mm)
     */
    private String repayDarkHours;

    @ApiModelProperty(value = "资金方授信黑暗期")
    private String fundingCreditDarkHours;

    /**
     * 资金方用信黑暗期
     */
    private String fundingLoanDarkHours;

    /**
     * 资金方还款黑暗期
     */
    private String fundingRepayDarkHours;

    /**
     * 日授信限额(万元)
     */
    private BigDecimal dailyCreditLimit;

    /**
     * 日放款限额(万元)
     */
    private BigDecimal dailyLoanLimit;

    /**
     * 授信锁定期限(天)
     */
    private Integer creditLockDays;

    /**
     * 用信锁定期限(天)
     */
    private Integer loanLockDays;

    /**
     * 对客利率(%)
     */
    private String customerInterestRate;

    /**
     * 对资利率(%)
     */
    private String fundingInterestRate;

    /**
     * 年龄范围(岁) (格式 如 22-55)
     */
    private String ageRange;

    /**
     * 支持的还款类型 (英文逗号分隔)
     */
    private RepaymentType supportedRepayTypes;


    /**
     * 借款期限 (英文逗号分隔)
     */
    private String loanTerms;

    /**
     * 资方路由
     */
    private String capitalRoute;

    /**
     * 项目时效类型（LONGTIME, TEMPORARY）
     */
    private ProjectDurationType projectDurationType;

    /**
     * 临时配置有效期起
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime tempStartTime;

    /**
     * 临时配置有效期止
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime tempEndTime;

    /**
     * 启用状态
     */
    private AbleStatus enabled;

    /**
     * 年结是否顺延
     */
    private String graceNext;

    /**
     * 版本号
     */
    @Version
    private Integer revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;
}
