/*
*  Copyright 2019-2025 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.jinghang.cash.modules.project.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
* @description /
* <AUTHOR>
* @date 2025-08-25
**/
@Data
public class ProjectElementsExtDto implements Serializable {

    @ApiModelProperty(value = "主键")
    private String id;

    @NotBlank
    @ApiModelProperty(value = "项目要素主键ID")
    private String parentId;

    @NotBlank
    @ApiModelProperty(value = "关联的项目唯一编码")
    private String projectCode;

    @ApiModelProperty(value = "年利率基数(天) (如 360或365)")
    private String interestDaysBasis;

    @ApiModelProperty(value = "是否支持线下跨日还款")
    private String allowCrossDayRepay;

    @ApiModelProperty(value = "风控模型渠道")
    private String riskModelChannel;

    @ApiModelProperty(value = "放款支付渠道")
    private String loanPaymentChannel;

    @ApiModelProperty(value = "扣款绑卡渠道")
    private String deductionBindCardChannel;

    @ApiModelProperty(value = "扣款商户号")
    private String deductionMerchantCode;

    @ApiModelProperty(value = "签章渠道")
    private String signChannel;

    @ApiModelProperty(value = "逾期短信发送方")
    private String overdueSmsSender;

    @ApiModelProperty(value = "短信渠道")
    private String smsChannel;

    @ApiModelProperty(value = "逾期宽限期类型 (SQ:首期, MQ:每期)")
    private String gracePeriodType;

    @ApiModelProperty(value = "逾期宽限期(天)")
    private String gracePeriodDays;

    @ApiModelProperty(value = "节假日是否顺延")
    private String holidayPostpone;

    @ApiModelProperty(value = "征信查询方")
    private String creditQueryParty;

    @ApiModelProperty(value = "征信上报方")
    private String creditReportSender;

    @ApiModelProperty(value = "催收方")
    private String collectionParty;

    @ApiModelProperty(value = "是否推送催收数据")
    private String pushCollectionData;

    @ApiModelProperty(value = "是否推送客诉数据")
    private String pushKsData;

    @ApiModelProperty(value = "是否支持催收减免")
    private String allowCollectionWaiver;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "乐观锁")
    private String revision;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    public void copy(ProjectElementsExtDto source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
