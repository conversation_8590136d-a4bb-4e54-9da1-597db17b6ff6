<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">项目编码</label>
        <el-input v-model="query.projectCode" clearable placeholder="请输入项目编码" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="请输入项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目类型</label>
        <el-select v-model="query.enabled" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 90px" @change="crud.toQuery">
          <el-option v-for="item in enabledTypeOptions" :key="item.key" :label="item.display_name" :value="item.key" />
        </el-select>
        <label class="el-form-item-label">项目状态</label>
        <el-select v-model="query.enabled" clearable size="small" placeholder="请选择项目状态" class="filter-item" style="width: 90px" @change="crud.toQuery">
          <el-option v-for="item in enabledTypeOptions" :key="item.key" :label="item.display_name" :value="item.key" />
        </el-select>
        <label class="el-form-item-label">资产方名称</label>
        <el-input v-model="query.flowChannel" clearable placeholder="请输入资产方名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">资金方名称</label>
        <el-input v-model="query.capitalChannel" clearable placeholder="请输入资金方名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" >
        <el-button slot="right"
          v-if="crud.optShow.add"
          v-permission="permission.dtl"
          class="filter-item"
          size="mini"
          type="info"
          icon="el-icon-document"
          @click="jumpPage"
        >
          查看
        </el-button>
        <el-button slot="right"
          v-if="crud.optShow.add"
          v-permission="permission.dtl"
          class="filter-item"
          size="mini"
          type="danger"
          icon="el-icon-circle-close"
          @click="stopUse"
        >
          停用
        </el-button>
      </crudOperation>
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="580px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
          <el-form-item label="项目名称" prop="projectName">
            <el-input v-model="form.projectName" style="width: 370px;" clearable placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="资产方" prop="flowChannel">
            <el-input v-model="form.flowChannel" style="width: 370px;" clearable placeholder="请输入资产方" />
          </el-form-item>
          <el-form-item label="融担方" prop="guaranteeCode">
            <el-input v-model="form.guaranteeCode" style="width: 370px;" clearable placeholder="请输入融担方" />
          </el-form-item>
          <el-form-item label="资金方" prop="capitalChannel">
            <el-input v-model="form.capitalChannel" style="width: 370px;" clearable placeholder="请输入资金方" />
          </el-form-item>
          <el-form-item label="项目类型" prop="projectTypeCode">
            <el-select v-model="query.enabled" clearable size="small" placeholder="请选择项目类型" class="filter-item" style="width: 370px;" @change="crud.toQuery">
              <el-option v-for="item in enabledTypeOptions" :key="item.key" :label="item.display_name" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="projectCode" label="项目编码" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="enabled" label="项目状态" />
        <el-table-column prop="flowChannel" label="资产方" />
        <el-table-column prop="guaranteeCode" label="融担方" />
        <el-table-column prop="capitalChannel" label="资金方" />
        <el-table-column prop="projectTypeCode" label="项目类型" />
        <el-table-column prop="dayCreditLimt" label="日授信限额" />
        <el-table-column prop="dayLoanLimit" label="日放款限额" />
        <el-table-column prop="updatedBy" label="更新人" />
        <el-table-column prop="updatedTime" label="更新时间" />

        <!-- <el-table-column v-if="checkPer(['admin','projectInfo:edit','projectInfo:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column> -->
      </el-table>
      <!--分页组件-->
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
    </div>
  </div>
</template>

<script>
import crudProject from '@/api/projectManage/project'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, projectCode: null, projectName: null, flowChannel: null, guaranteeCode: null, capitalChannel: null, projectTypeCode: null, enabled: null, startDate: null, endDate: null, remark: null, revision: null, createdBy: null, createdTime: null, updatedBy: null, updatedTime: null, ksSwitch: null, csSwitch: null, dayCreditLimt: null, dayLoanLimit: null }
export default {
  name: 'ProjectInfo',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '项目', url: 'api/projectInfo', idField: 'id', sort: 'id,desc', crudMethod: { ...crudProject }, optShow: {add: true, edit: true, reset: true}})
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        orderState: undefined,
        flowChannel: undefined,
        startTime: undefined,
        endTime: undefined,
        packageStatus: undefined,
        bankChannel:undefined,
      },
      permission: {
        add: ['admin', 'projectInfo:add'],
        edit: ['admin', 'projectInfo:edit'],
        dtl: ['admin', 'flowConfig:dtl'],
        use: ['admin', 'flowConfig:use']
      },
      rules: {
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        flowChannel: [
          { required: true, message: '资产方不能为空', trigger: 'blur' }
        ],
        guaranteeCode: [
          { required: true, message: '融担方不能为空', trigger: 'blur' }
        ],
        capitalChannel: [
          { required: true, message: '资金方不能为空', trigger: 'blur' }
        ],
        projectTypeCode: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'projectCode', display_name: '项目编码' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'flowChannel', display_name: '资产方名称' },
        { key: 'capitalChannel', display_name: '资金方名称' },
        { key: 'projectTypeCode', display_name: '项目类型' },
        { key: 'enabled', display_name: '项目状态' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
