package com.maguo.loan.cash.flow.entrance.common.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSSException;
import com.jinghang.cash.api.dto.ProjectAgreementDto;
import com.jinghang.cash.api.enums.TemplateOwner;
import com.jinghang.common.util.CollectionUtil;
import com.maguo.loan.cash.flow.entity.AgreementSignatureRecord;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.SftpRestoreRecord;
import com.maguo.loan.cash.flow.entity.UserFile;
import com.maguo.loan.cash.flow.entrance.common.dto.request.SftpRestoreFileReqDTO;
import com.maguo.loan.cash.flow.enums.FileType;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.job.agreement.LoanAgreementJob;
import com.maguo.loan.cash.flow.job.ppd.PPDSignJob;
import com.maguo.loan.cash.flow.remote.manage.ProjectAgreementFeign;
import com.maguo.loan.cash.flow.repository.AgreementSignatureRecordRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.SftpRestoreRecordRepository;
import com.maguo.loan.cash.flow.repository.UserFileRepository;
import com.maguo.loan.cash.flow.service.UserFileService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * sftp 文件恢复服务
 *
 * <AUTHOR>
 */
@Service
public class SftpRestoreFileService {

    private static final Logger logger = LoggerFactory.getLogger(SftpRestoreFileService.class);

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private LoanRepository loanRepository;
    @Autowired
    private UserFileService userFileService;
    @Autowired
    private UserFileRepository userFileRepository;
    @Autowired
    private PPDSignJob ppdSignJob;
    @Autowired
    private LoanAgreementJob loanAgreementJob;
    @Autowired
    private AgreementSignatureRecordRepository agreementSignatureRecordRepository;
    @Autowired
    private SftpRestoreRecordRepository sftpRestoreRecordRepository;
    @Autowired
    private ProjectAgreementFeign projectAgreementFeign;

    private static final List<FileType> CREDIT_FILE_TYPES = Arrays.asList(
            FileType.LETTER_OF_COMMITMENT,
            FileType.CONSULTING_SERVICE_CONTRACT,
            FileType.AUTHORIZATION_LETTER_FOR_ENTRUSTED_DEDUCTION_GUARANTEE,
            FileType.PERSONAL_INFORMATION_AUTHORIZATION_LETTER,
            FileType.COMPREHENSIVE_AUTHORIZATION_LETTER_GUARANTEE
    );

    private static final List<FileType> LOAN_FILE_TYPES = Arrays.asList(
            FileType.LOAN_CONTRACT,
            FileType.ENTRUSTED_DEDUCTION_LETTER,
            FileType.DIGITAL_CERTIFICATE_AUTHORIZATION_LETTER,
            FileType.SYNTHESIS_AUTHORIZATION,
            FileType.ARBITRATION_AGREEMENT,
            FileType.PERSONAL_CREDIT_AUTHORIZATION_LETTER_CREDIT
    );

    private static final String FILE_NOT_EXISTS = "文件记录不存在";

    public String restoreFile(SftpRestoreFileReqDTO request) {
        logger.info("SftpRestoreFileService.restoreFile start, params:{}", JSON.toJSONString(request));
        String sftpPath = "";
        try {
            String outerOrderId = request.getOuterOrderId();
            FileType fileType = FileType.valueOf(request.getFileType());
            List<SftpRestoreRecord> byOuterOrderIdAndApplyTimeAfter = sftpRestoreRecordRepository.findByOuterOrderIdAndFileTypeAndApplyTimeAfterOrderByCreatedTimeDesc(outerOrderId, fileType, LocalDateTime.now().minusDays(7));
            if (CollectionUtil.isNotEmpty(byOuterOrderIdAndApplyTimeAfter)) {
                return byOuterOrderIdAndApplyTimeAfter.get(0).getSftpPath();
            }
            Order order = orderRepository.findByOuterOrderId(outerOrderId);
            if (ObjectUtils.isEmpty(order)) {
                return "该订单不存在！";
            }
            FlowChannel flowChannel = order.getFlowChannel();
            Loan loan = loanRepository.findByOrderId(order.getId());
            if (ObjectUtils.isEmpty(loan)) {
                return "该订单不存在！";
            }
            SftpRestoreRecord sftpRestoreRecord = new SftpRestoreRecord();
            sftpRestoreRecord.setFileType(fileType);
            sftpRestoreRecord.setFlowChannel(flowChannel);
            sftpRestoreRecord.setApplyTime(LocalDateTime.now());
            sftpRestoreRecord.setOuterOrderId(outerOrderId);
            if (FlowChannel.PPCJDL.equals(flowChannel)) {//拍拍
                //恢复合同-拍拍
                UserFile userFile;
                //获取合同模板配置信息
                ProjectAgreementDto agreementDto = getProjectAgreementDto(loan,fileType.name());
                if(Objects.nonNull(agreementDto)){
                    String templateOwner = agreementDto.getTemplateOwner();//模板归属方
                    if(Objects.equals(templateOwner, TemplateOwner.CAPITAL.name())){//资金方
                        userFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                    }else{//融担方
                        userFile = userFileRepository.findLatestUserFileByOrderNoAndFileType(outerOrderId, fileType.name()).orElse(null);
                    }
                    if (ObjectUtils.isEmpty(userFile)) {
                        return FILE_NOT_EXISTS;//文件记录不存在
                    }
                    sftpPath = ppdSignJob.uploadSignFileToSftp(loan, userFile, agreementDto.getCapitalContractName());
                }
            }
            if (FlowChannel.LVXIN.equals(flowChannel)) {
                // 恢复合同-绿信
                ProjectAgreementDto agreementDto = getProjectAgreementDto(loan, fileType.name());
                if (agreementDto == null) {
                    return "未找到该文件的命名配置";
                }

                Optional<AgreementSignatureRecord> recordOpt =
                    agreementSignatureRecordRepository.findLatestAgreementSignatureRecordByOrderNoAndFileType(
                        outerOrderId, fileType.name()
                    );

                if (recordOpt.isPresent()) {
                    AgreementSignatureRecord record = recordOpt.get();
                    sftpPath = loanAgreementJob.uploadFlowAgreementFileToSftp(
                        loan,
                        record.getCommonOssUrl(),
                        record.getFileType(),
                        order,
                        agreementDto.getCapitalContractName()
                    );
                } else {
                    // 签署记录没有，再查用户文件
                    UserFile userFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(
                        loan.getId(), fileType
                    );
                    if (ObjectUtils.isEmpty(userFile)) {
                        return FILE_NOT_EXISTS;
                    }
                    sftpPath = loanAgreementJob.uploadCoreAgreementFileToSftp(
                        loan,
                        userFile,
                        order,
                        agreementDto.getCapitalContractName()
                    );
                }
            }
            //恢复结清证明
            if (fileType.equals(FileType.CREDIT_SETTLE_VOUCHER_FILE)) {
                if (!order.getOrderState().equals(OrderState.CLEAR)) {
                    return "该笔订单尚未结清！";
                }
                UserFile voucherFile = userFileRepository.findTopByLoanNoAndFileTypeOrderByCreatedTimeDesc(loan.getId(), fileType);
                if (ObjectUtils.isEmpty(voucherFile)) {
                    return FILE_NOT_EXISTS;
                }
                sftpPath = userFileService.uploadVoucherFileToSftp(voucherFile.getOssKey(), voucherFile.getOssBucket(), loan, voucherFile.getFileType(), order);
            }
            sftpRestoreRecord.setSftpPath(sftpPath);
            sftpRestoreRecordRepository.save(sftpRestoreRecord);
        } catch (IllegalArgumentException e) {
            logger.error("sftp恢复文件失败", e);
            return "文件类型不正确";
        }catch (OSSException e){
            logger.error("sftp恢复文件失败", e);
            return "OSS文件不存在";
        }catch (Exception e) {
            logger.error("sftp恢复文件失败", e);
            return "error";
        }
        logger.info("SftpRestoreFileService.restoreFile end.");
        return sftpPath;
    }

    /**
     * 获取合同模板配置信息
     * @param loan 借据信息
     * @param fileType 文件类型
     * @return 配置信息
     */
    public ProjectAgreementDto getProjectAgreementDto(Loan loan, String fileType) {
        logger.info("根据项目唯一编码" + loan.getProjectCode() + "和文件类型" + fileType + "查询合同模板配置信息");
        //根据项目唯一编码和文件类型查询合同模板配置信息
       return projectAgreementFeign.getByStageAndType(loan.getProjectCode(), null, null, fileType);
    }

}
