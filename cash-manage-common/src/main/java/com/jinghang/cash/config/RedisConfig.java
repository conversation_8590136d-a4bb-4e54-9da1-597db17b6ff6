/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.jinghang.cash.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;


/**
 * Redis 配置类 - 适用于阿里云 Redis 集群
 * 使用 Jackson 序列化器替代 FastJSON，提供更好的稳定性和安全性
 *
 * <AUTHOR> Jie
 * @date 2018-11-24
 * @updated 2025-08-26 适配阿里云集群
 */
@Slf4j
@Configuration
@EnableCaching
@ConditionalOnClass(RedisOperations.class)
@EnableConfigurationProperties(RedisProperties.class)
public class RedisConfig extends CachingConfigurerSupport {

    /**
     * 创建自定义的 ObjectMapper，用于 Redis 序列化
     * 支持 Java 8 时间类型和类型信息保存
     */
    @Bean
    public ObjectMapper redisObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 设置可见性
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        // 启用类型信息，用于反序列化时确定具体类型
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,
                ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);

        // 注册 Java 8 时间模块
        mapper.registerModule(new JavaTimeModule());

        // 禁用时间戳格式
        mapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        return mapper;
    }

    /**
     * 设置 redis 数据默认过期时间，默认2小时
     * 设置@cacheable 序列化方式
     */
    @Bean
    public RedisCacheConfiguration redisCacheConfiguration() {
        // 使用 Jackson 序列化器
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(redisObjectMapper());

        RedisCacheConfiguration configuration = RedisCacheConfiguration.defaultCacheConfig()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer))
                .entryTtl(Duration.ofHours(2));

        return configuration;
    }

    /**
     * 配置 CacheManager - 支持多个缓存实例
     * 为不同的业务模块创建专门的缓存配置
     */
    @Bean
    @Override
    public org.springframework.cache.CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 使用 Jackson 序列化器
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(redisObjectMapper());

        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer))
                .entryTtl(Duration.ofHours(2));

        // 为不同缓存设置不同的过期时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // 数据权限缓存 - 1小时过期
        cacheConfigurations.put("data", defaultConfig.entryTtl(Duration.ofHours(1)));

        // 菜单缓存 - 2小时过期
        cacheConfigurations.put("menu", defaultConfig.entryTtl(Duration.ofHours(2)));

        // 部门缓存 - 1小时过期
        cacheConfigurations.put("dept", defaultConfig.entryTtl(Duration.ofHours(1)));

        // 用户缓存 - 30分钟过期
        cacheConfigurations.put("user", defaultConfig.entryTtl(Duration.ofMinutes(30)));

        // 角色缓存 - 1小时过期
        cacheConfigurations.put("role", defaultConfig.entryTtl(Duration.ofHours(1)));

        log.info("初始化 Redis CacheManager，支持缓存: {}", cacheConfigurations.keySet());

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * RedisTemplate 配置 - 适用于阿里云 Redis 集群
     * 使用 Jackson 序列化器，提供更好的兼容性和安全性
     */
    @SuppressWarnings("all")
    @Bean(name = "redisTemplate")
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();

        // 使用 Jackson 序列化器
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(redisObjectMapper());
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        // key 采用 String 的序列化方式
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);

        // value 采用 Jackson 的序列化方式
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        // 设置连接工厂
        template.setConnectionFactory(redisConnectionFactory);

        // 初始化 RedisTemplate
        template.afterPropertiesSet();

        log.info("Redis Template initialized with Jackson serializer for Alibaba Cloud Redis Cluster");

        return template;
    }

    /**
     * 自定义缓存key生成策略，默认将使用该策略
     * 优化后的 key 生成策略，更适合集群环境
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder keyBuilder = new StringBuilder();

            // 类名
            keyBuilder.append(target.getClass().getSimpleName()).append(":");
            // 方法名
            keyBuilder.append(method.getName()).append(":");

            // 参数处理
            if (params.length > 0) {
                for (Object param : params) {
                    if (param != null) {
                        keyBuilder.append(param.toString()).append(":");
                    } else {
                        keyBuilder.append("null:");
                    }
                }
                // 移除最后一个冒号
                keyBuilder.setLength(keyBuilder.length() - 1);
            }

            String key = keyBuilder.toString();

            // 如果 key 太长，使用 SHA256 摘要
            if (key.length() > 200) {
                return DigestUtils.sha256Hex(key);
            }

            return key;
        };
    }

    /**
     * Redis 缓存异常处理器
     * 当 Redis 发生异常时，记录日志但不影响业务流程
     * 适合集群环境下的容错处理
     */
    @Bean
    @Override
    @SuppressWarnings("all")
    public CacheErrorHandler errorHandler() {
        log.info("初始化 Redis CacheErrorHandler for Alibaba Cloud Redis Cluster");
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException e, Cache cache, Object key) {
                log.error("Redis Cluster - Cache GET error, cache: [{}], key: [{}]",
                    cache.getName(), key, e);
            }

            @Override
            public void handleCachePutError(RuntimeException e, Cache cache, Object key, Object value) {
                log.error("Redis Cluster - Cache PUT error, cache: [{}], key: [{}], value type: [{}]",
                    cache.getName(), key, value != null ? value.getClass().getSimpleName() : "null", e);
            }

            @Override
            public void handleCacheEvictError(RuntimeException e, Cache cache, Object key) {
                log.error("Redis Cluster - Cache EVICT error, cache: [{}], key: [{}]",
                    cache.getName(), key, e);
            }

            @Override
            public void handleCacheClearError(RuntimeException e, Cache cache) {
                log.error("Redis Cluster - Cache CLEAR error, cache: [{}]",
                    cache.getName(), e);
            }
        };
    }
}
